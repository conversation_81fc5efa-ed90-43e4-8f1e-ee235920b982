#!/usr/bin/env python3
"""
Test script to verify Gmail connection and settings
"""
import json
import sys

def test_settings():
    """Test if settings.json is properly configured"""
    try:
        with open("settings.json", "r", encoding="utf8") as f:
            settings = json.load(f)
        
        required_fields = ["email", "gmailPass", "eMailEnd", "password"]
        missing_fields = []
        
        for field in required_fields:
            if field not in settings or not settings[field] or settings[field].startswith("your_") or settings[field].startswith("كلمة_"):
                missing_fields.append(field)
        
        if missing_fields:
            print("❌ Settings not configured properly!")
            print(f"Please update these fields in settings.json: {missing_fields}")
            return False
        
        print("✅ Settings file looks good!")
        return True
        
    except FileNotFoundError:
        print("❌ settings.json file not found!")
        return False
    except json.JSONDecodeError:
        print("❌ Invalid JSON in settings.json!")
        return False

def test_gmail_connection():
    """Test Gmail connection"""
    try:
        import gmailReader as gr
        print("🔄 Testing Gmail connection...")
        
        # Test connection without reading emails
        import imaplib
        with open("settings.json", "r", encoding="utf8") as f:
            settings = json.load(f)
        
        email = settings["email"] + "@" + settings["eMailEnd"]
        password = settings["gmailPass"]
        
        imap = imaplib.IMAP4_SSL("imap.gmail.com")
        imap.login(email, password)
        imap.select("INBOX")
        imap.close()
        
        print("✅ Gmail connection successful!")
        return True
        
    except Exception as e:
        print(f"❌ Gmail connection failed: {e}")
        print("Make sure you have:")
        print("1. Enabled 2FA on your Google account")
        print("2. Generated an App Password")
        print("3. Used the App Password (not your regular password)")
        return False

def main():
    print("🧪 Testing TikTok Account Creator Setup")
    print("=" * 40)
    
    if not test_settings():
        sys.exit(1)
    
    if not test_gmail_connection():
        sys.exit(1)
    
    print("\n🎉 All tests passed! You can now run the main script.")
    print("Run: python tiktok.py")

if __name__ == "__main__":
    main()
