from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.firefox.service import Service
from webdriver_manager.firefox import GeckoDriverManager
import time
import json
import random

# Import

data = open("settings.json", "r+", encoding="utf8")
jsondata = json.load(data)
data.close()

emailEnd = jsondata["eMailEnd"]
passw = jsondata["password"]
email = jsondata["email"]
emailFull = email + "+" + str(random.randint(1, 99999)) + "@" + emailEnd

# Setup Firefox with modern Selenium
options = webdriver.FirefoxOptions()
#options.add_argument('--headless')  # Uncomment for headless mode

# Use WebDriverManager to automatically handle driver
service = Service(GeckoDriverManager().install())
browser = webdriver.Firefox(service=service, options=options)
browser.implicitly_wait(10)

# Setup WebDriverWait
wait = WebDriverWait(browser, 10)


# Functions
def getGmail():
    import gmailReader as gr
    print("getMail")
    code = gr.getmail()
    try:
        codePath = wait.until(EC.presence_of_element_located((By.XPATH, '/html/body/div[1]/div/div[1]/form/div[4]/div[5]/div/input')))
        if not (codePath.get_property("value") == code):
            try:
                codePath.clear()
            except:
                print("Deletion could not be performed")
            codePath.send_keys(code)
            Register()
        else:
            if browser.current_url is None:
                exit()
            else:
                getGmail()
    except Exception as e:
        print(f"Error finding code input: {e}")
        time.sleep(2)
        getGmail()


def Register():
    global emailFull, passw
    try:
        # Click register button
        register_btn = wait.until(EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div/div[1]/form/div[4]/button')))
        register_btn.click()
        time.sleep(1)

        # Check for result message
        try:
            result = wait.until(EC.presence_of_element_located((By.XPATH, '/html/body/div[1]/div/div[1]/form/div[4]/div[6]/div')))
            result_text = result.get_attribute("innerHTML")

            if result_text == "Incorrect code":
                time.sleep(1)
                getGmail()
            elif "Verification failed" in result_text:
                print("Verification failed. Please click Resend and try again.")
                time.sleep(1)
                Register()
            elif "blocked" in result_text.lower():
                print("YOUR IP BLOCKED!")
            else:
                try:
                    skipPath = wait.until(EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div/div[1]/form/button[2]")))
                    print("The account has been created, the mails are deleted and the new account is transferred")
                    skipPath.click()

                    import gmailReader as gr
                    gr.deletemail()
                    browser.quit()
                except:
                    print("no account opened")
        except:
            # If no error message found, assume success
            try:
                skipPath = wait.until(EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div/div[1]/form/button[2]")))
                print("The account has been created, the mails are deleted and the new account is transferred")
                skipPath.click()
                successReg(emailFull, passw)
                import gmailReader as gr
                gr.deletemail()
                browser.quit()
            except:
                print("Registration process unclear")
    except Exception as e:
        print(f"Registration error: {e}")
        try:
            print(f"Current URL: {browser.current_url}")
        except:
            print("Browser connection lost")
            browser.quit()
            exit()

        try:
            skipPath = wait.until(EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div/div[1]/form/button[2]")))
            print("The account has been created, the mails are deleted and the new account is transferred")
            skipPath.click()
            successReg(emailFull, passw)
            import gmailReader as gr
            gr.deletemail()
            browser.quit()
            import os, sys
            os.startfile(__file__)
            sys.exit()
        except:
            if (browser.current_url == "https://www.tiktok.com/login/download-app"):
                successReg(emailFull, passw)
                import gmailReader as gr
                gr.deletemail()
                browser.quit()
                import os, sys
                os.startfile(__file__)
                sys.exit()
            print("You did not enter the code, try again")
            time.sleep(1)
            Register()


def successReg(email, password):
    veri = open("users.txt", "a")
    veri.write(email + ":" + password + "\n")


# Functions end
cookies = "tt_webid=6884877451467179525; tt_webid_v2=6884877451467179525; s_v_web_id=verify_kgev18p4_wS31yIAd_DbQv_4d9Y_944R_3giruvGhbAwM; _ga=GA1.2.1495663218.1603010452; _gid=GA1.2.*********.1603010452; passport_csrf_token=1686c19f83b8da0c0dbf1634f55f933c; odin_tt=e93bab047745d7a898fcdd155f575acb181bd17df20c8d98bc5963ad3292070aaf51b5e4388c883a8c17965c0dd5b852532e783b6b792ea69624dcff21466a6b; store-idc=maliva; store-country-code=tr; sid_guard=e41bbe2a5233598221d4d35fed7b67cb%7C1603011367%7C21600%7CSun%2C+18-Oct-2020+14%3A56%3A07+GMT; uid_tt=8492306fe5c1e3c2b593491b1a2b9c21; uid_tt_ss=8492306fe5c1e3c2b593491b1a2b9c21; sid_tt=e41bbe2a5233598221d4d35fed7b67cb; sessionid=e41bbe2a5233598221d4d35fed7b67cb; sessionid_ss=e41bbe2a5233598221d4d35fed7b67cb; MONITOR_WEB_ID=6884877451467179525".split(
    ";")


try:
    browser.get("https://www.tiktok.com/signup/phone-or-email/email")

    # Wait for page to load and find email/password fields
    emailPath = wait.until(EC.presence_of_element_located((By.NAME, "email")))
    passPath = wait.until(EC.presence_of_element_located((By.NAME, "password")))

    print(f"Creating account with email: {emailFull}")
    emailPath.send_keys(emailFull)
    passPath.send_keys(passw)

    # Select birth date (month)
    month_dropdown = wait.until(EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div/div[1]/form/div[2]/div[1]/div")))
    month_dropdown.click()
    month_option = wait.until(EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div/div[1]/form/div[2]/div[1]/ul/li[5]")))
    month_option.click()

    # Click outside to close dropdown
    browser.find_element(By.XPATH, "/html/body/div[1]/div").click()
    time.sleep(0.5)

    # Select birth date (day)
    day_dropdown = wait.until(EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div/div[1]/form/div[2]/div[2]/div")))
    day_dropdown.click()
    day_option = wait.until(EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div/div[1]/form/div[2]/div[2]/ul/li[5]")))
    day_option.click()

    # Click outside to close dropdown
    browser.find_element(By.XPATH, "/html/body/div[1]/div").click()
    time.sleep(0.5)

    # Select birth date (year)
    year_dropdown = wait.until(EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div/div[1]/form/div[2]/div[3]/div")))
    year_dropdown.click()
    year_option = wait.until(EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div/div[1]/form/div[2]/div[3]/ul/li[29]")))
    year_option.click()

    # Click outside to close dropdown
    browser.find_element(By.XPATH, "/html/body/div[1]/div").click()
    time.sleep(0.5)

    # Click send code button
    send_code_btn = wait.until(EC.element_to_be_clickable((By.XPATH, "/html/body/div[1]/div/div[1]/form/div[4]/div[5]/button")))
    send_code_btn.click()

    print("Verification code sent, waiting for email...")
    time.sleep(3)  # Wait for email to arrive
    getGmail()

except Exception as e:
    print(f"Error during signup process: {e}")
    browser.quit()
