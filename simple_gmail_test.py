#!/usr/bin/env python3
"""
Simple Gmail connection test
"""
import imaplib
import json

def test_gmail():
    try:
        # Load settings
        with open("settings.json", "r", encoding="utf8") as f:
            settings = json.load(f)
        
        email = settings["email"] + "@" + settings["eMailEnd"]
        password = settings["gmailPass"]
        
        print(f"Testing connection to: {email}")
        print(f"Using password: {password[:4]}{'*' * (len(password)-4)}")
        
        # Try different IMAP servers
        servers = [
            ("imap.gmail.com", 993),
            ("imap.gmail.com", 143)
        ]
        
        for server, port in servers:
            try:
                print(f"\nTrying {server}:{port}...")
                if port == 993:
                    imap = imaplib.IMAP4_SSL(server, port)
                else:
                    imap = imaplib.IMAP4(server, port)
                    imap.starttls()
                
                print("Connected to server, attempting login...")
                result = imap.login(email, password)
                print(f"Login result: {result}")
                
                imap.select("INBOX")
                print("✅ Successfully connected to Gmail!")
                
                # Check inbox
                typ, data = imap.search(None, 'ALL')
                print(f"Found {len(data[0].split()) if data[0] else 0} emails in inbox")
                
                imap.close()
                imap.logout()
                return True
                
            except Exception as e:
                print(f"❌ Failed with {server}:{port} - {e}")
                continue
        
        return False
        
    except Exception as e:
        print(f"❌ General error: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Simple Gmail Connection Test")
    print("=" * 35)
    test_gmail()
